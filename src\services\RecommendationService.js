const { UserPreference, ProductUsages, Product, Question, Option } = require('../models');
const mongoose = require('mongoose');

/**
 * Enhanced Recommendation Service
 * Provides sophisticated product recommendations based on user preferences
 */
class RecommendationService {
  
  /**
   * Get recommended products for a user with advanced matching
   * @param {ObjectId} userId - User ID
   * @param {Object} options - Pagination and filtering options
   * @returns {Promise<Object>} - Paginated recommendations with match scores
   */
  static async getRecommendedProducts(userId, options = {}) {
    const { page = 1, limit = 10, minMatchScore = 0 } = options;
    const skip = (page - 1) * limit;

    try {
      // Get user preferences with question details
      const userPreferences = await UserPreference.find({ user: userId })
        .populate({
          path: 'question',
          select: '_id value sequence',
          match: { deleted: { $ne: true } }
        })
        .populate({
          path: 'answer',
          select: '_id value',
          match: { deleted: { $ne: true } }
        });

      if (!userPreferences || userPreferences.length === 0) {
        return {
          results: [],
          page,
          limit,
          totalPages: 0,
          totalResults: 0,
          message: 'No preferences found. Please set your preferences to get personalized recommendations.'
        };
      }

      // Filter valid preferences
      const validPreferences = userPreferences.filter(pref => pref.question && pref.answer);
      
      if (validPreferences.length === 0) {
        return {
          results: [],
          page,
          limit,
          totalPages: 0,
          totalResults: 0,
          message: 'No valid preferences found.'
        };
      }

      // Get all products with their usages and calculate match scores
      const recommendedProducts = await this.calculateProductMatchScores(validPreferences, minMatchScore);

      // Apply pagination
      const totalResults = recommendedProducts.length;
      const totalPages = Math.ceil(totalResults / limit);
      const paginatedResults = recommendedProducts.slice(skip, skip + limit);

      // Calculate match score range for better user feedback
      const matchScores = recommendedProducts.map(p => p.matchScore);
      const highestScore = matchScores.length > 0 ? Math.max(...matchScores) : 0;
      const lowestScore = matchScores.length > 0 ? Math.min(...matchScores) : 0;

      let message = `Found ${totalResults} products matching your preferences!`;
      if (totalResults > 0) {
        if (highestScore === lowestScore) {
          message += ` All products have ${highestScore}% match.`;
        } else {
          message += ` Match scores range from ${highestScore}% to ${lowestScore}% (sorted high to low).`;
        }
      }

      return {
        results: paginatedResults,
        page,
        limit,
        totalPages,
        totalResults,
        userPreferences: validPreferences.length,
        highestMatchScore: highestScore,
        lowestMatchScore: lowestScore,
        message
      };

    } catch (error) {
      console.error('Error in getRecommendedProducts:', error);
      throw new Error('Failed to get product recommendations');
    }
  }

  /**
   * Calculate match scores for products based on user preferences
   * @param {Array} userPreferences - User's preferences with populated question/answer
   * @param {Number} minMatchScore - Minimum match score threshold
   * @returns {Promise<Array>} - Products with match scores, sorted by relevance
   */
  static async calculateProductMatchScores(userPreferences, minMatchScore = 0) {
    // Create preference map for quick lookup
    const preferenceMap = new Map();
    const questionWeights = new Map();
    
    userPreferences.forEach(pref => {
      const questionId = pref.question._id.toString();
      const optionId = pref.answer._id.toString();
      
      preferenceMap.set(questionId, optionId);
      // Weight questions by sequence (lower sequence = higher importance)
      const weight = pref.question.sequence ? (100 - pref.question.sequence) / 100 : 1;
      questionWeights.set(questionId, Math.max(weight, 0.1)); // Minimum weight of 0.1
    });

    // Get all products with their usages using aggregation
    const productsWithUsages = await Product.aggregate([
      {
        $lookup: {
          from: 'productusages',
          localField: '_id',
          foreignField: 'product',
          as: 'productUsages',
          pipeline: [
            { $match: { deleted: { $ne: true } } },
            {
              $lookup: {
                from: 'questions',
                localField: 'question',
                foreignField: '_id',
                as: 'questionDetails'
              }
            },
            {
              $lookup: {
                from: 'options',
                localField: 'option',
                foreignField: '_id',
                as: 'optionDetails'
              }
            }
          ]
        }
      },
      {
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'categoryDetails'
        }
      },
      {
        $lookup: {
          from: 'subcategories',
          localField: 'subCategory',
          foreignField: '_id',
          as: 'subCategoryDetails'
        }
      },
      {
        $lookup: {
          from: 'providers',
          localField: 'provider',
          foreignField: '_id',
          as: 'providerDetails'
        }
      },
      {
        $lookup: {
          from: 'variants',
          localField: '_id',
          foreignField: 'product',
          as: 'variants'
        }
      }
    ]);

    // Calculate match scores for each product
    const scoredProducts = [];
    
    for (const product of productsWithUsages) {
      const matchScore = this.calculateProductScore(product, preferenceMap, questionWeights);
      
      if (matchScore.totalScore >= minMatchScore) {
        scoredProducts.push({
          _id: product._id,
          name: product.name,
          description: product.description,
          photo: product.photo,
          category: product.categoryDetails[0]?.name || 'Unknown',
          subCategory: product.subCategoryDetails[0]?.name || 'Unknown',
          provider: {
            _id: product.providerDetails[0]?._id,
            name: product.providerDetails[0]?.name || 'Unknown Provider'
          },
          variants: product.variants,
          matchScore: matchScore.totalScore,
          matchDetails: matchScore.details,
          exactMatches: matchScore.exactMatches,
          partialMatches: matchScore.partialMatches,
          createdAt: product.createdAt
        });
      }
    }

    // Sort by match score (descending: 100% to 0%), then by creation date (newest first)
    return scoredProducts.sort((a, b) => {
      // Primary sort: Match score (high to low)
      if (b.matchScore !== a.matchScore) {
        return b.matchScore - a.matchScore; // 100% first, then 90%, 80%... 0% last
      }

      // Secondary sort: Exact matches count (high to low)
      if (b.exactMatches !== a.exactMatches) {
        return b.exactMatches - a.exactMatches;
      }

      // Tertiary sort: Creation date (newest first)
      return new Date(b.createdAt) - new Date(a.createdAt);
    });
  }

  /**
   * Calculate match score for a single product
   * @param {Object} product - Product with usages
   * @param {Map} preferenceMap - User preferences map (questionId -> optionId)
   * @param {Map} questionWeights - Question importance weights
   * @returns {Object} - Match score details
   */
  static calculateProductScore(product, preferenceMap, questionWeights) {
    let totalScore = 0;
    let exactMatches = 0;
    let partialMatches = 0;
    const matchDetails = [];
    const maxPossibleScore = Array.from(questionWeights.values()).reduce((sum, weight) => sum + weight, 0);

    // Check each product usage against user preferences
    for (const usage of product.productUsages) {
      const questionId = usage.question.toString();
      const productOptionId = usage.option.toString();
      const userPreferredOptionId = preferenceMap.get(questionId);
      const questionWeight = questionWeights.get(questionId) || 0;

      if (userPreferredOptionId) {
        if (productOptionId === userPreferredOptionId) {
          // Exact match - full weight
          totalScore += questionWeight;
          exactMatches++;
          matchDetails.push({
            questionId,
            questionValue: usage.questionDetails[0]?.value || 'Unknown Question',
            userOption: usage.optionDetails[0]?.value || 'Unknown Option',
            productOption: usage.optionDetails[0]?.value || 'Unknown Option',
            matchType: 'exact',
            score: questionWeight
          });
        } else {
          // Check for partial match (same question, different option)
          const partialScore = questionWeight * 0.3; // 30% of full weight for partial match
          totalScore += partialScore;
          partialMatches++;
          matchDetails.push({
            questionId,
            questionValue: usage.questionDetails[0]?.value || 'Unknown Question',
            userOption: 'Different Option',
            productOption: usage.optionDetails[0]?.value || 'Unknown Option',
            matchType: 'partial',
            score: partialScore
          });
        }
      }
    }

    // Normalize score to percentage (0-100)
    const normalizedScore = maxPossibleScore > 0 ? (totalScore / maxPossibleScore) * 100 : 0;

    return {
      totalScore: Math.round(normalizedScore * 100) / 100, // Round to 2 decimal places
      exactMatches,
      partialMatches,
      details: matchDetails
    };
  }

  /**
   * Get products by category with recommendations
   * @param {ObjectId} userId - User ID
   * @param {ObjectId} categoryId - Category ID
   * @param {Object} options - Pagination options
   * @returns {Promise<Object>} - Category products with recommendation scores
   */
  static async getRecommendedProductsByCategory(userId, categoryId, options = {}) {
    const { page = 1, limit = 10 } = options;
    const skip = (page - 1) * limit;

    try {
      // Get user preferences
      const userPreferences = await UserPreference.find({ user: userId })
        .populate('question', '_id value sequence')
        .populate('answer', '_id value');

      const validPreferences = userPreferences.filter(pref => pref.question && pref.answer);

      // Get products in category
      const categoryProducts = await Product.aggregate([
        { $match: { category: mongoose.Types.ObjectId(categoryId) } },
        {
          $lookup: {
            from: 'productusages',
            localField: '_id',
            foreignField: 'product',
            as: 'productUsages',
            pipeline: [
              { $match: { deleted: { $ne: true } } },
              {
                $lookup: {
                  from: 'questions',
                  localField: 'question',
                  foreignField: '_id',
                  as: 'questionDetails'
                }
              },
              {
                $lookup: {
                  from: 'options',
                  localField: 'option',
                  foreignField: '_id',
                  as: 'optionDetails'
                }
              }
            ]
          }
        },
        {
          $lookup: {
            from: 'categories',
            localField: 'category',
            foreignField: '_id',
            as: 'categoryDetails'
          }
        },
        {
          $lookup: {
            from: 'subcategories',
            localField: 'subCategory',
            foreignField: '_id',
            as: 'subCategoryDetails'
          }
        },
        {
          $lookup: {
            from: 'providers',
            localField: 'provider',
            foreignField: '_id',
            as: 'providerDetails'
          }
        },
        {
          $lookup: {
            from: 'variants',
            localField: '_id',
            foreignField: 'product',
            as: 'variants'
          }
        }
      ]);

      // Calculate scores if user has preferences
      let scoredProducts = categoryProducts;
      if (validPreferences.length > 0) {
        const preferenceMap = new Map();
        const questionWeights = new Map();

        validPreferences.forEach(pref => {
          const questionId = pref.question._id.toString();
          const optionId = pref.answer._id.toString();
          preferenceMap.set(questionId, optionId);
          const weight = pref.question.sequence ? (100 - pref.question.sequence) / 100 : 1;
          questionWeights.set(questionId, Math.max(weight, 0.1));
        });

        scoredProducts = categoryProducts.map(product => {
          const matchScore = this.calculateProductScore(product, preferenceMap, questionWeights);
          return {
            _id: product._id,
            name: product.name,
            description: product.description,
            photo: product.photo,
            category: product.categoryDetails[0]?.name || 'Unknown',
            subCategory: product.subCategoryDetails[0]?.name || 'Unknown',
            provider: {
              _id: product.providerDetails[0]?._id,
              name: product.providerDetails[0]?.name || 'Unknown Provider'
            },
            variants: product.variants,
            matchScore: matchScore.totalScore,
            matchDetails: matchScore.details,
            exactMatches: matchScore.exactMatches,
            partialMatches: matchScore.partialMatches,
            createdAt: product.createdAt
          };
        });

        // Sort by match score (descending: 100% to 0%), then by exact matches, then by date
        scoredProducts.sort((a, b) => {
          // Primary sort: Match score (high to low)
          if (b.matchScore !== a.matchScore) {
            return b.matchScore - a.matchScore; // 100% first, then 90%, 80%... 0% last
          }

          // Secondary sort: Exact matches count (high to low)
          if (b.exactMatches !== a.exactMatches) {
            return b.exactMatches - a.exactMatches;
          }

          // Tertiary sort: Creation date (newest first)
          return new Date(b.createdAt) - new Date(a.createdAt);
        });
      }

      // Apply pagination
      const totalResults = scoredProducts.length;
      const totalPages = Math.ceil(totalResults / limit);
      const paginatedResults = scoredProducts.slice(skip, skip + limit);

      // Calculate match score range for category products
      const matchScores = scoredProducts.map(p => p.matchScore || 0);
      const highestScore = matchScores.length > 0 ? Math.max(...matchScores) : 0;
      const lowestScore = matchScores.length > 0 ? Math.min(...matchScores) : 0;

      let message = `Found ${totalResults} products in category!`;
      if (totalResults > 0 && validPreferences.length > 0) {
        if (highestScore === lowestScore && highestScore > 0) {
          message += ` All products have ${highestScore}% match.`;
        } else if (highestScore > 0) {
          message += ` Match scores range from ${highestScore}% to ${lowestScore}% (sorted high to low).`;
        } else {
          message += ` Products sorted by creation date (no preference matches found).`;
        }
      } else if (totalResults > 0) {
        message += ` Products sorted by creation date (no user preferences set).`;
      }

      return {
        results: paginatedResults,
        page,
        limit,
        totalPages,
        totalResults,
        userPreferences: validPreferences.length,
        highestMatchScore: highestScore,
        lowestMatchScore: lowestScore,
        message
      };

    } catch (error) {
      console.error('Error in getRecommendedProductsByCategory:', error);
      throw new Error('Failed to get category recommendations');
    }
  }
}

module.exports = RecommendationService;
